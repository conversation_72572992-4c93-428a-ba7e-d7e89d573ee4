# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2024-10"

[[extensions]]
name = "discount-gift-countdown-apply"
handle = "discount-gift-countdown-apply"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.checkout.reductions.render-after"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
network_access = true

# Add network access rules - specify allowed domains
[[extensions.capabilities.network.access_rules]]
target = "https://*.ecoflow.com"
type = "http"

[[extensions.capabilities.network.access_rules]]
target = "https://*.myshopify.com"
type = "http"

# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]
  [[extensions.settings.fields]]
  key = "api_domain"
  type = "single_line_text_field"
  name = "API Domain"
  description = "Input UAT or Production API domain to switch between environments"

  [[extensions.settings.fields]]
  key = "api_x_appid"
  type = "single_line_text_field"
  name = "API x-appid"
  description = "Appid for different countries"

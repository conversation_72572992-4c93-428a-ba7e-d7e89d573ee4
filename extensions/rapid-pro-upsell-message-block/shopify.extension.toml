# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-04"

[[extensions]]
name = "rapid-pro-upsell-message-block"
handle = "rapid-pro-upsell-message-block"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.checkout.cart-line-list.render-after"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
network_access = true



# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]
[[extensions.settings.fields]]
key = "wn_product_product_ids"
type = "multi_line_text_field"
name = "WN Product IDs"
description = "Enter the product IDs of the products you want to show the banner for. Separate each ID with a comma and no spaces between."

[[extensions.settings.fields]]
key = "smart_device_product_product_ids"
type = "multi_line_text_field"
name = "Smart Device Product IDs"
description = "Enter the product IDs of the products you want to show the banner for. Separate each ID with a comma and no spaces between."

[[extensions.settings.fields]]
key = "rapid_pro_product_product_ids"
type = "multi_line_text_field"
name = "RAPID Pro Product IDs"
description = "Enter the product IDs of the products you want to show the banner for. Separate each ID with a comma and no spaces between."

[[extensions.settings.fields]]
key = "three_c_product_product_ids"
type = "multi_line_text_field"
name = "3C Product IDs"
description = "Enter the product IDs of the products you want to show the banner for. Separate each ID with a comma and no spaces between."

[[extensions.settings.fields]]
key = "rapid_pro_promotion_message_image_url"
type = "single_line_text_field"
name = "RAPID Pro Promotion Message Image URL"
description = "Enter the URL of the image you want to show in the banner."

[[extensions.settings.fields]]
key = "wn_promotion_message_text"
type = "single_line_text_field"
name = "WN Promotion Message Text"
description = "Message shown when only a WN product is in the cart and no rapid pro product is in the cart."

[[extensions.settings.fields]]
key = "smart_device_promotion_message_text"
type = "single_line_text_field"
name = "Smart Device Promotion Message Text"
description = "Message shown when only a Smart Device product is in the cart and no rapid pro product is in the cart."

[[extensions.settings.fields]]
key = "wn_smart_device_promotion_message_text"
type = "single_line_text_field"
name = "WN + Smart Device Promotion Message Text"
description = "Message shown when a WN product and a Smart Device product are in the cart and no rapid pro product is in the cart."

[[extensions.settings.fields]]
key = "three_c_promotion_message_image_url"
type = "single_line_text_field"
name = "3C Accessories Promotion Message Image URL"
description = "Enter the URL of the image you want to show in the banner."

[[extensions.settings.fields]]
key = "wn_smart_device_rapid_pro_promotion_message_text"
type = "single_line_text_field"
name = "WN / Smart Device + RAPID Pro Promotion Message Text"
description = "Message shown when a WN product or a Smart Device product, and a RAPID Pro product are in the cart."

[[extensions.settings.fields]]
key = "three_c_promotion_message_text"
type = "single_line_text_field"
name = "3C Promotion Message Text"
description = "Message shown when only a WN or Smart Device product, and arapid pro product is in the cart. Or when WN or Smart Device is not in the cart and 3C is in the cart."


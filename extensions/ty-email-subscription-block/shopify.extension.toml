# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-04"

[[extensions]]
name = "ty-email-subscription-block"
handle = "ty-email-subscription-block"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.thank-you.block.render"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
network_access = true

# Add network access rules - specify allowed domains
[[extensions.capabilities.network.access_rules]]
target = "https://*.ecoflow.com"
type = "http"

[[extensions.capabilities.network.access_rules]]
target = "https://*.myshopify.com"
type = "http"

# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]
[[extensions.settings.fields]]
key = "api_url"
type = "single_line_text_field"
name = "API URL"
description = "Enter the API URL"

[[extensions.settings.fields]]
key = "x_app_id"
type = "single_line_text_field"
name = "X App ID"
description = "Enter the X App ID"

[[extensions.settings.fields]]
key = "accepted_language"
type = "single_line_text_field"
name = "Accepted Language"
description = "Enter the accepted language"

[[extensions.settings.fields]]
key = "api_key"
type = "single_line_text_field"
name = "API Key"
description = "Enter the API Key"

[[extensions.settings.fields]]
key = "contact_list_id"
type = "single_line_text_field"
name = "Contact List ID"
description = "Enter the Emarsys Contact List ID"

[[extensions.settings.fields]]
key = "single_field_id_1"
type = "single_line_text_field"
name = "Single Field ID 1"
description = "Enter the first Emarsys Single Field ID"

[[extensions.settings.fields]]
key = "single_field_value_1"
type = "single_line_text_field"
name = "Single Field Value 1"
description = "Enter the first Emarsys Single Field Value"

[[extensions.settings.fields]]
key = "single_field_overwrite_1"
type = "boolean"
name = "Single Field Overwrite 1"
description = "The first Emarsys Single Field Overwrite value"

[[extensions.settings.fields]]
key = "single_field_id_2"
type = "single_line_text_field"
name = "Single Field ID 2"
description = "Enter the second Emarsys Single Field ID"

[[extensions.settings.fields]]
key = "single_field_value_2"
type = "single_line_text_field"
name = "Single Field Value 2"
description = "Enter the second Emarsys Single Field Value"

[[extensions.settings.fields]]
key = "single_field_overwrite_2"
type = "boolean"
name = "Single Field Overwrite 2"
description = "The second Emarsys Single Field Overwrite value"

[[extensions.settings.fields]]
key = "multiple_field_id_1"
type = "single_line_text_field"
name = "Multiple Field ID 1"
description = "Enter the first Emarsys Multiple Field ID"

[[extensions.settings.fields]]
key = "multiple_field_value_1"
type = "single_line_text_field"
name = "Multiple Field Value 1"
description = "Enter the first Emarsys Multiple Field Value, separated by commas, do not add space after comma. E.g. 1,2,3"

[[extensions.settings.fields]]
key = "multiple_field_overwrite_1"
type = "boolean"
name = "Multiple Field Overwrite 1"
description = "The first Emarsys Multiple Field Overwrite value"

[[extensions.settings.fields]]
key = "form_title"
type = "single_line_text_field"
name = "Form Title"
description = "Enter the section title"

[[extensions.settings.fields]]
key = "form_description"
type = "single_line_text_field"
name = "Form Description"
description = "Enter the section description"

[[extensions.settings.fields]]
key = "input_label"
type = "single_line_text_field"
name = "Input Label"
description = "Enter the email input field label"

[[extensions.settings.fields]]
key = "submit_button_text"
type = "single_line_text_field"
name = "Submit Button Text"
description = "Enter the submit button text"

[[extensions.settings.fields]]
key = "privacy_policy_link"
type = "single_line_text_field"
name = "Privacy Policy Link"
description = "Enter the privacy policy link, this will be shown in the disclaimer"

[[extensions.settings.fields]]
key = "terms_of_service_link"
type = "single_line_text_field"
name = "Terms of Service Link"
description = "Enter the terms of service link, this will be shown in the disclaimer"

# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "6ccc3c854a0a81180f9075d44e0a55c5"
name = "Ecoflow Checkout Widgets Dev"
handle = "ecoflow-checkout-widgets-dev-1"
application_url = "https://shopify.dev/apps/default-app-home"
embedded = true

[build]
dev_store_url = "ecoflow-wj-app.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_checkouts,read_customers,read_discounts,read_products,unauthenticated_read_checkouts,unauthenticated_read_customers"

[auth]
redirect_urls = [ "https://shopify.dev/apps/default-app-home/api/auth" ]

[webhooks]
api_version = "2024-04"

[pos]
embedded = false

# Learn more about configuring your checkout UI extension:
# https://shopify.dev/api/checkout-extensions/checkout/configuration

# The version of APIs your extension will receive. Learn more:
# https://shopify.dev/docs/api/usage/versioning
api_version = "2025-04"

[[extensions]]
name = "signature-service-addon-block"
handle = "signature-service-addon-block"
type = "ui_extension"


# Controls where in Shopify your extension will be injected,
# and the file that contains your extension’s source code. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/extension-targets-overview

[[extensions.targeting]]
module = "./src/Checkout.tsx"
target = "purchase.checkout.block.render"

[extensions.capabilities]
# Gives your extension access to directly query Shopify’s storefront API.
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#api-access
api_access = true

# Gives your extension access to make external network calls, using the
# JavaScript `fetch()` API. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#network-access
network_access = true

# Add network access rules - specify allowed domains
[[extensions.capabilities.network.access_rules]]
target = "https://*.ecoflow.com"
type = "http"

[[extensions.capabilities.network.access_rules]]
target = "https://*.myshopify.com"
type = "http"

# Loads metafields on checkout resources, including the cart,
# products, customers, and more. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#metafields

# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_key"
# [[extensions.metafields]]
# namespace = "my_namespace"
# key = "my_other_key"

# Defines settings that will be collected from merchants installing
# your extension. Learn more:
# https://shopify.dev/docs/api/checkout-ui-extensions/unstable/configuration#settings-definition

[extensions.settings]
[[extensions.settings.fields]]
key = "signature_service_variant_id"
type = "single_line_text_field"
name = "Signature Service Variant ID"
description = "Enter the variant ID of the signature service, this is used to add the signature service to the cart"

[[extensions.settings.fields]]
key = "signature_service_product_id"
type = "single_line_text_field"
name = "Signature Service Product ID"
description = "Enter the product ID of the signature service"

[[extensions.settings.fields]]
key = "signature_service_title"
type = "single_line_text_field"
name = "Signature Service Title"
description = "Enter the title of the signature service"

[[extensions.settings.fields]]
key = "signature_service_description"
type = "single_line_text_field"
name = "Signature Service Description"
description = "Enter the description of the signature service"

[[extensions.settings.fields]]
key = "no_signature_service_option_label"
type = "single_line_text_field"
name = "No Signature Service Option Label"
description = "Enter the label of the no signature service option"

[[extensions.settings.fields]]
key = "signature_service_option_label"
type = "single_line_text_field"
name = "Signature Service Option Label"
description = "Enter the label of the signature service option"
{"name": "ecoflow-checkout-widgets-app", "version": "1.0.0", "license": "UNLICENSED", "scripts": {"shopify": "shopify", "build": "shopify app build", "dev": "shopify app dev", "use-dev": "shopify app config use shopify.app.toml", "use-prod": "shopify app config use shopify.app.production.toml", "info": "shopify app info", "generate": "shopify app generate", "deploy": "shopify app deploy", "deploy-dev": "shopify app config use shopify.app.toml && shopify app deploy", "deploy-prod": "shopify app config use shopify.app.production.toml && shopify app deploy"}, "dependencies": {"@shopify/app": "3.58.2", "@shopify/cli": "3.58.2", "react": "^18.2.0", "react-dom": "^18.2.0"}, "trustedDependencies": ["@shopify/plugin-cloudflare"], "author": "kyle<PERSON>u", "private": true, "workspaces": ["extensions/*"]}